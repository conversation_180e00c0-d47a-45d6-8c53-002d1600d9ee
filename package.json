{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@libsql/client": "^0.15.10", "@nuxthub/core": "^0.9.0", "@nuxtjs/tailwindcss": "^6.14.0", "@pinia/nuxt": "^0.11.2", "drizzle-orm": "^0.44.4", "nuxt": "^3.18.1", "nuxt-auth-utils": "^0.5.23", "pinia": "^3.0.3", "radix-vue": "^1.9.17", "shadcn-vue": "^2.2.0", "vue": "^3.5.18", "vue-router": "^4.5.1"}}